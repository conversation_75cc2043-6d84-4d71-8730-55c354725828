const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 4000;

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:8081', 'http://***********:8081'],
  credentials: true
}));

app.use(express.json());

// Route de test
app.get('/', (req, res) => {
  console.log('✅ Route / appelée');
  res.json({
    message: 'Serveur AquaTrack fonctionnel',
    status: 'OK',
    port: PORT,
    timestamp: new Date().toISOString()
  });
});

// Route d'authentification (sans base de données)
app.post('/api/auth/login', (req, res) => {
  console.log('🔐 Tentative de connexion:', req.body);
  const { email, password } = req.body;
  
  if (email === '<EMAIL>' && password === 'Tech123') {
    console.log('✅ Connexion ré<NAME_EMAIL>');
    res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        idtech: 1,
        nom: 'Technicien',
        prenom: 'Test',
        email: '<EMAIL>',
        role: 'Tech'
      },
      token: 'test-token-123'
    });
  } else if (email === '<EMAIL>' && password === 'Admin123') {
    console.log('✅ Connexion ré<NAME_EMAIL>');
    res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        idtech: 2,
        nom: 'Admin',
        prenom: 'Test',
        email: '<EMAIL>',
        role: 'Admin'
      },
      token: 'test-token-456'
    });
  } else {
    console.log('❌ Échec de connexion pour:', email);
    res.status(401).json({
      success: false,
      message: 'Email ou mot de passe incorrect'
    });
  }
});

// Route pour les clients (données de test)
app.get('/api/clients', (req, res) => {
  console.log('📋 Route /api/clients appelée');
  const testClients = [
    {
      idclient: 1,
      nom: 'Benali',
      prenom: 'Fatima',
      adresse: '45 Avenue Hassan II, près de l\'école Omar Ibn Al Khattab',
      ville: 'Setrou',
      tel: '0612345678',
      email: '<EMAIL>',
      statut: 'Actif'
    },
    {
      idclient: 2,
      nom: 'Alami',
      prenom: 'Mohammed',
      adresse: '12 Rue des Oliviers',
      ville: 'Setrou',
      tel: '0623456789',
      email: '<EMAIL>',
      statut: 'Actif'
    }
  ];
  
  res.json({
    success: true,
    clients: testClients
  });
});

// Route pour les contrats d'un client
app.get('/api/clients/:id/contracts', (req, res) => {
  console.log('📋 Route /api/clients/:id/contracts appelée pour ID:', req.params.id);
  const testContracts = [
    {
      idcontract: 1,
      codeqr: 'QR-TEST-RFNAI1',
      datecontract: '2023-07-03',
      marquecompteur: 'SAGEMCOM TEST',
      numseriecompteur: 'TEST-001'
    }
  ];
  
  res.json({
    success: true,
    count: testContracts.length,
    data: testContracts
  });
});

// Gestion des erreurs
app.use((err, req, res, next) => {
  console.error('❌ Erreur serveur:', err);
  res.status(500).json({
    success: false,
    message: 'Erreur interne du serveur'
  });
});

// Démarrage du serveur
app.listen(PORT, '0.0.0.0', () => {
  console.log('🚀 Serveur AquaTrack TEST démarré avec succès !');
  console.log(`📡 URL: http://localhost:${PORT}`);
  console.log(`🌐 Accessible sur toutes les interfaces réseau`);
  console.log(`🔧 Routes disponibles:`);
  console.log(`   - GET  /                           - Test serveur`);
  console.log(`   - POST /api/auth/login             - Authentification`);
  console.log(`   - GET  /api/clients                - Liste clients`);
  console.log(`   - GET  /api/clients/:id/contracts  - Contrats client`);
  console.log('✅ Serveur prêt à recevoir des connexions');
});

module.exports = app;
