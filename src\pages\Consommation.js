import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Alert,
  ActivityIndicator
} from 'react-native';

// Composant Picker personnalisé compatible React Native Web
const CustomPicker = ({ selectedValue, onValueChange, children, style }) => {
  return (
    <select
      value={selectedValue}
      onChange={(e) => onValueChange(e.target.value)}
      style={{
        width: '100%',
        padding: 10, // Réduit le padding
        fontSize: 14, // Réduit la taille de police
        height: 40, // Hauteur fixe plus petite
        borderWidth: 1,
        borderColor: '#d1d5db',
        borderRadius: 6, // Réduit le rayon des bordures
        backgroundColor: 'white',
        ...style
      }}
    >
      {children}
    </select>
  );
};

const PickerItem = ({ label, value }) => {
  return <option value={value}>{label}</option>;
};

const Consommation = () => {
  const [formData, setFormData] = useState({
    periode: '',
    idClient: '',
    idContract: '',
    idSecteur: '',
    consommationActuelle: '',
    consommationPre: '',
    jours: ''
  });
  const [clients, setClients] = useState([]);
  const [contracts, setContracts] = useState([]);
  const [secteurs, setSecteurs] = useState([]);
  const [clientsDuSecteur, setClientsDuSecteur] = useState([]);
  const [secteurSelectionne, setSecteurSelectionne] = useState(null);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  useEffect(() => {
    fetchClients();
    fetchSecteurs();
  }, []);

  const fetchClients = async () => {
    try {
      console.log('🔍 Récupération des clients...');
      const response = await fetch('http://localhost:4000/api/clients');
      if (response.ok) {
        const result = await response.json();
        console.log('📊 Réponse API clients:', result);

        // L'API retourne { success: true, data: [...], total: X }
        if (result.success && Array.isArray(result.data)) {
          setClients(result.data);
          console.log('✅ Clients chargés:', result.data.length);
        } else {
          console.error('❌ Format de réponse inattendu:', result);
          setClients([]);
        }
      } else {
        console.error('❌ Erreur HTTP:', response.status);
        setClients([]);
      }
    } catch (err) {
      console.error('❌ Erreur lors du chargement des clients:', err);
      setClients([]);
    }
  };

  const fetchContracts = async (clientId) => {
    try {
      console.log('🔍 Récupération des contrats pour le client:', clientId);
      // Utiliser l'API correcte pour récupérer les contrats d'un client
      const response = await fetch(`http://localhost:4000/api/clients/${clientId}/contracts`);
      if (response.ok) {
        const result = await response.json();
        console.log('📊 Réponse API contrats:', result);

        // L'API retourne { success: true, data: [...], count: X }
        if (result.success && Array.isArray(result.data)) {
          setContracts(result.data);
          console.log('✅ Contrats chargés:', result.data.length);

          if (result.data.length === 1) {
            setFormData(prev => ({ ...prev, idContract: result.data[0].idcontract }));
            console.log('✅ Contrat unique sélectionné automatiquement');
          }
        } else {
          console.error('❌ Format de réponse inattendu:', result);
          setContracts([]);
        }
      } else {
        console.error('❌ Erreur HTTP:', response.status);
        setContracts([]);
      }
    } catch (err) {
      console.error('❌ Erreur lors du chargement des contrats:', err);
      setContracts([]);
    }
  };

  const fetchSecteurs = async () => {
    try {
      console.log('🔍 Récupération des secteurs...');
      const response = await fetch('http://localhost:4000/api/secteurs');
      if (response.ok) {
        const result = await response.json();
        console.log('📊 Réponse API secteurs:', result);

        // L'API retourne { success: true, data: [...], total: X }
        if (result.success && Array.isArray(result.data)) {
          setSecteurs(result.data);
          console.log('✅ Secteurs chargés:', result.data.length);
        } else {
          console.error('❌ Format de réponse inattendu:', result);
          setSecteurs([]);
        }
      } else {
        console.error('❌ Erreur HTTP:', response.status);
        setSecteurs([]);
      }
    } catch (err) {
      console.error('❌ Erreur lors du chargement des secteurs:', err);
      setSecteurs([]);
    }
  };

  const fetchClientsDuSecteur = async (secteurId) => {
    try {
      console.log('🔍 Récupération des clients du secteur:', secteurId);
      const response = await fetch(`http://localhost:4000/api/secteurs/${secteurId}/clients`);
      if (response.ok) {
        const result = await response.json();
        console.log('📊 Réponse API clients du secteur:', result);

        if (result.success && Array.isArray(result.data)) {
          setClientsDuSecteur(result.data);
          console.log('✅ Clients du secteur chargés:', result.data.length);

          // Ouvrir Google Maps avec tous les clients du secteur
          if (result.data.length > 0) {
            ouvrirGoogleMapsAvecClients(result.data);
          }
        } else {
          console.error('❌ Format de réponse inattendu:', result);
          setClientsDuSecteur([]);
        }
      } else {
        console.error('❌ Erreur HTTP:', response.status);
        setClientsDuSecteur([]);
      }
    } catch (err) {
      console.error('❌ Erreur lors du chargement des clients du secteur:', err);
      setClientsDuSecteur([]);
    }
  };

  const ouvrirGoogleMapsAvecClients = (clients) => {
    try {
      console.log('🗺️ Ouverture de Google Maps avec', clients.length, 'clients');

      if (clients.length === 0) {
        Alert.alert('Information', 'Aucun client trouvé dans ce secteur');
        return;
      }

      // Créer les marqueurs pour chaque client avec leurs coordonnées
      const marqueurs = clients.map((client, index) => {
        const lat = client.latitude || (33.5731 + Math.random() * 0.01);
        const lng = client.longitude || (-7.5898 + Math.random() * 0.01);
        console.log(`📍 Client ${index + 1}: ${client.nom} ${client.prenom} - Lat: ${lat}, Lng: ${lng}`);
        return `${lat},${lng}`;
      });

      // Construire l'URL Google Maps avec plusieurs marqueurs
      const premierMarqueur = marqueurs[0];
      const tousLesMarqueurs = marqueurs.join('|');

      // URL Google Maps avec marqueurs multiples
      const googleMapsUrl = `https://www.google.com/maps?q=${premierMarqueur}&markers=${tousLesMarqueurs}`;

      // Ouvrir dans un nouvel onglet
      window.open(googleMapsUrl, '_blank');

      console.log('✅ Google Maps ouvert avec', clients.length, 'clients du secteur');
      console.log('🔗 URL:', googleMapsUrl);
    } catch (error) {
      console.error('❌ Erreur ouverture Google Maps:', error);
      Alert.alert('Erreur', 'Impossible d\'ouvrir Google Maps');
    }
  };

  const handleSecteurChange = (secteurId) => {
    console.log('🔄 Changement de secteur:', secteurId);
    setFormData(prev => ({ ...prev, idSecteur: secteurId }));

    if (secteurId) {
      // Trouver le secteur sélectionné
      const secteur = secteurs.find(s => s.ids === parseInt(secteurId));
      setSecteurSelectionne(secteur);

      // Récupérer et afficher les clients de ce secteur
      fetchClientsDuSecteur(secteurId);
    } else {
      setSecteurSelectionne(null);
      setClientsDuSecteur([]);
    }
  };



  const handleSubmit = async () => {
    setLoading(true);
    setMessage('');

    // Validation
    if (parseFloat(formData.consommationActuelle) <= parseFloat(formData.consommationPre)) {
      Alert.alert('Erreur de validation', 'La consommation actuelle doit être supérieure à la consommation précédente');
      setLoading(false);
      return;
    }

    try {
      const response = await fetch('http://localhost:4000/api/consommations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          periode: formData.periode,
          consommationPre: parseInt(formData.consommationPre || 0),
          consommationActuelle: parseInt(formData.consommationActuelle),
          jours: parseInt(formData.jours || 30),
          idcont: formData.idContract,
          idsecteur: formData.idSecteur,
          idtech: 1, // ID du technicien connecté
          idtranch: 1,
          status: 'En cours'
        }),
      });

      const result = await response.json();

      if (result.success) {
        Alert.alert('Succès', 'Consommation enregistrée avec succès!');
        setFormData({
          periode: '',
          idClient: '',
          idContract: '',
          idSecteur: '',
          consommationActuelle: '',
          consommationPre: '',
          jours: ''
        });
        setContracts([]);
      } else {
        Alert.alert('Erreur', result.message || 'Erreur lors de l\'enregistrement');
      }
    } catch (err) {
      Alert.alert('Erreur de connexion', 'Impossible de se connecter au serveur');
    } finally {
      setLoading(false);
    }
  };

  const goBack = () => {
    // Pour React Native, vous pouvez utiliser la navigation
    // navigation.goBack(); // Si vous utilisez React Navigation
    console.log('Retour demandé');
  };

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={goBack} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Retour</Text>
        </TouchableOpacity>
        <Text style={styles.title}>💧 Saisie Consommation</Text>
      </View>

      {/* Form Container */}
      <View style={styles.formContainer}>
        {/* Secteur - PREMIER CHAMP */}
        <View style={[styles.formGroup, styles.firstFormGroup]}>
          <Text style={[styles.label, styles.firstLabel]}>📍 Secteur</Text>
          <CustomPicker
            selectedValue={formData.idSecteur}
            onValueChange={handleSecteurChange}
          >
            <PickerItem label="Sélectionner un secteur" value="" />
            {Array.isArray(secteurs) && secteurs.map(secteur => (
              <PickerItem
                key={secteur.ids}
                label={secteur.nom}
                value={secteur.ids}
              />
            ))}
          </CustomPicker>
        </View>

        {/* Période */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>Période (YYYY-MM)</Text>
          <TextInput
            style={styles.input}
            value={formData.periode}
            onChangeText={(text) => setFormData(prev => ({ ...prev, periode: text }))}
            placeholder="2025-01"
            maxLength={7}
          />
        </View>

        {/* Client */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>Client</Text>
          <CustomPicker
            selectedValue={formData.idClient}
            onValueChange={(value) => {
              setFormData(prev => ({ ...prev, idClient: value, idContract: '' }));
              if (value) {
                fetchContracts(value);
              } else {
                setContracts([]);
              }
            }}
          >
            <PickerItem label="Sélectionner un client" value="" />
            {Array.isArray(clients) && clients.map(client => (
              <PickerItem
                key={client.idclient}
                label={`${client.nom} ${client.prenom}`}
                value={client.idclient}
              />
            ))}
          </CustomPicker>
        </View>

        {/* Contrat */}
        {Array.isArray(contracts) && contracts.length > 0 && (
          <View style={styles.formGroup}>
            <Text style={styles.label}>Contrat</Text>
            {contracts.length === 1 ? (
              <TextInput
                style={[styles.input, styles.readOnlyInput]}
                value={`Contrat ${contracts[0].idcontract} - ${contracts[0].marquecompteur || 'N/A'}`}
                editable={false}
              />
            ) : (
              <CustomPicker
                selectedValue={formData.idContract}
                onValueChange={(value) => setFormData(prev => ({ ...prev, idContract: value }))}
              >
                <PickerItem label="Sélectionner un contrat" value="" />
                {contracts.map(contract => (
                  <PickerItem
                    key={contract.idcontract}
                    label={`Contrat ${contract.idcontract} - ${contract.marquecompteur || 'N/A'}`}
                    value={contract.idcontract}
                  />
                ))}
              </CustomPicker>
            )}
          </View>
        )}

        {/* Consommation Précédente */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>Consommation Précédente (m³)</Text>
          <TextInput
            style={styles.input}
            value={formData.consommationPre}
            onChangeText={(text) => setFormData(prev => ({ ...prev, consommationPre: text }))}
            placeholder="0"
            keyboardType="numeric"
          />
        </View>

        {/* Consommation Actuelle */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>Consommation Actuelle (m³)</Text>
          <TextInput
            style={styles.input}
            value={formData.consommationActuelle}
            onChangeText={(text) => setFormData(prev => ({ ...prev, consommationActuelle: text }))}
            placeholder="Saisir la consommation actuelle"
            keyboardType="numeric"
          />
        </View>

        {/* Nombre de jours */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>Nombre de jours</Text>
          <TextInput
            style={styles.input}
            value={formData.jours}
            onChangeText={(text) => setFormData(prev => ({ ...prev, jours: text }))}
            placeholder="30"
            keyboardType="numeric"
          />
        </View>

        {/* Message */}
        {message && (
          <View style={[styles.message, message.includes('succès') ? styles.successMessage : styles.errorMessage]}>
            <Text style={styles.messageText}>{message}</Text>
          </View>
        )}

        {/* Submit Button */}
        <TouchableOpacity
          style={[styles.submitButton, loading && styles.disabledButton]}
          onPress={handleSubmit}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator color="#fff" />
          ) : (
            <Text style={styles.submitButtonText}>Enregistrer la Consommation</Text>
          )}
        </TouchableOpacity>
      </View>
    </ScrollView>

  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 20,
    marginBottom: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  backButton: {
    backgroundColor: '#6366f1',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    marginRight: 20,
  },
  backButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  formContainer: {
    backgroundColor: 'white',
    margin: 20,
    marginHorizontal: 40, // Augmente les marges horizontales
    maxWidth: 500, // Limite la largeur maximale
    alignSelf: 'center', // Centre le formulaire
    padding: 20,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  formGroup: {
    marginBottom: 15, // Réduit l'espacement entre les champs
  },
  firstFormGroup: {
    backgroundColor: '#f0f8ff', // Fond bleu clair pour le premier champ
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#007bff',
    marginBottom: 20, // Plus d'espace après le premier champ
  },
  label: {
    fontSize: 14, // Réduit la taille de police
    fontWeight: '500',
    color: '#374151',
    marginBottom: 6, // Réduit l'espacement
  },
  firstLabel: {
    fontSize: 16, // Plus grand pour le premier champ
    fontWeight: 'bold',
    color: '#007bff', // Couleur bleue
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 6, // Réduit le rayon des bordures
    padding: 10, // Réduit le padding
    fontSize: 14, // Réduit la taille de police
    backgroundColor: 'white',
    height: 40, // Hauteur fixe plus petite
  },
  readOnlyInput: {
    backgroundColor: '#f9fafb',
    color: '#6b7280',
  },
  message: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 20,
  },
  successMessage: {
    backgroundColor: '#d1fae5',
    borderColor: '#a7f3d0',
    borderWidth: 1,
  },
  errorMessage: {
    backgroundColor: '#fee2e2',
    borderColor: '#fecaca',
    borderWidth: 1,
  },
  messageText: {
    fontSize: 14,
    fontWeight: '500',
  },
  submitButton: {
    backgroundColor: '#10b981',
    padding: 12, // Réduit le padding
    borderRadius: 6, // Réduit le rayon des bordures
    alignItems: 'center',
    marginTop: 10,
    height: 45, // Hauteur fixe plus petite
  },
  disabledButton: {
    backgroundColor: '#9ca3af',
  },
  submitButtonText: {
    color: 'white',
    fontSize: 14, // Réduit la taille de police
    fontWeight: 'bold',
  },
});

export default Consommation;
